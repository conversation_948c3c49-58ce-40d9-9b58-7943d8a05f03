{"name": "magnet-admin-api", "version": "1.0.0", "description": "Admin API for Magnet application", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:reload": "pm2 reload ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:logs": "pm2 logs magnet-admin-api", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "logs": "tail -f logs/combined.log", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["api", "express", "admin", "magnet"], "author": "", "license": "ISC", "devDependencies": {"nodemon": "^3.1.10", "pm2": "^6.0.8"}, "dependencies": {"express": "^4.21.2"}}