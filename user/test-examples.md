# User API Test Examples

## 1. Register Admin User

```bash
curl -X POST http://localhost:3000/api/user/register-admin \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123",
    "confirmPassword": "password123",
    "name": "<PERSON>",
    "surname": "<PERSON><PERSON>",
    "nickname": "<PERSON>",
    "phoneNumber": "0812345678",
    "email": "<EMAIL>",
    "type": "admin"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "ลงทะเบียนสำเร็จ",
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "name": "<PERSON>",
      "surname": "<PERSON><PERSON>",
      "nickname": "<PERSON>",
      "phoneNumber": "0999999999",
      "email": "<EMAIL>",
      "type": "admin",
      "createdAt": "2025-07-26T03:00:14.622Z",
      "updatedAt": "2025-07-26T03:00:14.622Z",
      "isActive": true
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": "24h"
  }
}
```

## 2. Get User Profile (Protected Route)

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:3000/api/user/profile
```

## 3. Get All Users (Protected Route)

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:3000/api/user/users
```

## 4. Verify JWT Token

```bash
curl -X POST http://localhost:3000/api/user/verify-token \
  -H "Content-Type: application/json" \
  -d '{"token": "YOUR_JWT_TOKEN"}'
```

## Error Examples

### Missing Required Fields
```json
{
  "success": false,
  "message": "กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน",
  "error": "MISSING_REQUIRED_FIELDS"
}
```

### Password Mismatch
```json
{
  "success": false,
  "message": "รหัสผ่านและยืนยันรหัสผ่านไม่ตรงกัน",
  "error": "PASSWORD_MISMATCH"
}
```

### Username Already Exists
```json
{
  "success": false,
  "message": "ชื่อผู้ใช้นี้มีอยู่ในระบบแล้ว",
  "error": "USERNAME_EXISTS"
}
```

### Invalid Token
```json
{
  "success": false,
  "message": "Access Token ไม่ถูกต้องหรือหมดอายุ",
  "error": "INVALID_TOKEN"
}
```

## Required Fields for Registration

- **username**: ชื่อผู้ใช้ (required)
- **password**: รหัสผ่าน (required, min 6 characters)
- **confirmPassword**: ยืนยันรหัสผ่าน (required, must match password)
- **name**: ชื่อจริง (required)
- **surname**: นามสกุล (required)
- **nickname**: ชื่อเล่น (optional)
- **phoneNumber**: เบอร์โทรศัพท์ (optional, 10 digits)
- **email**: อีเมล (required, valid email format)
- **type**: ประเภทผู้ใช้ (required, e.g., "admin", "user")
