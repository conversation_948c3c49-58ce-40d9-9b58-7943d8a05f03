const express = require('express');
const { registerAdmin, getAllUsers, verifyToken } = require('./user');

const router = express.Router();

/**
 * Middleware to verify JWT token
 */
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'ไม่พบ Access Token',
      error: 'NO_TOKEN'
    });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(403).json({
      success: false,
      message: 'Access Token ไม่ถูกต้องหรือหมดอายุ',
      error: 'INVALID_TOKEN'
    });
  }

  req.user = decoded;
  next();
};

/**
 * POST /api/user/register-admin
 * Register new admin user
 */
router.post('/register-admin', async (req, res) => {
  try {
    const result = await registerAdmin(req.body);
    
    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Register admin error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * GET /api/user/users
 * Get all users (protected route)
 */
router.get('/users', authenticateToken, (req, res) => {
  try {
    const users = getAllUsers();
    res.json({
      success: true,
      message: 'ดึงข้อมูลผู้ใช้สำเร็จ',
      data: users
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * GET /api/user/profile
 * Get current user profile (protected route)
 */
router.get('/profile', authenticateToken, (req, res) => {
  try {
    res.json({
      success: true,
      message: 'ดึงข้อมูลโปรไฟล์สำเร็จ',
      data: req.user
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลโปรไฟล์',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * POST /api/user/verify-token
 * Verify JWT token
 */
router.post('/verify-token', (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'กรุณาส่ง token',
        error: 'NO_TOKEN'
      });
    }

    const decoded = verifyToken(token);
    if (decoded) {
      res.json({
        success: true,
        message: 'Token ถูกต้อง',
        data: decoded
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'Token ไม่ถูกต้องหรือหมดอายุ',
        error: 'INVALID_TOKEN'
      });
    }
  } catch (error) {
    console.error('Verify token error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการตรวจสอบ token',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

module.exports = router;
