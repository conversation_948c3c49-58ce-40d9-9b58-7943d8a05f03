const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// JWT Secret (in production, use environment variable)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// In-memory storage for demo (in production, use database)
const users = [];

/**
 * Register Admin Function
 * @param {Object} userData - User registration data
 * @param {string} userData.username - Username
 * @param {string} userData.password - Password
 * @param {string} userData.confirmPassword - Confirm Password
 * @param {string} userData.name - First Name
 * @param {string} userData.surname - Last Name
 * @param {string} userData.nickname - Nickname
 * @param {string} userData.phoneNumber - Phone Number
 * @param {string} userData.email - Email Address
 * @param {string} userData.type - User Type (admin, user, etc.)
 * @returns {Object} Registration result
 */
const registerAdmin = async (userData) => {
  try {
    const {
      username,
      password,
      confirmPassword,
      name,
      surname,
      nickname,
      phoneNumber,
      email,
      type
    } = userData;

    // Validation
    if (!username || !password || !confirmPassword || !name || !surname || !email || !type) {
      return {
        success: false,
        message: 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน',
        error: 'MISSING_REQUIRED_FIELDS'
      };
    }

    // Check if passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        message: 'รหัสผ่านและยืนยันรหัสผ่านไม่ตรงกัน',
        error: 'PASSWORD_MISMATCH'
      };
    }

    // Check password strength
    if (password.length < 6) {
      return {
        success: false,
        message: 'รหัสผ่านต้องมีความยาวอย่างน้อย 6 ตัวอักษร',
        error: 'PASSWORD_TOO_SHORT'
      };
    }

    // Check if username already exists
    const existingUser = users.find(user => user.username === username);
    if (existingUser) {
      return {
        success: false,
        message: 'ชื่อผู้ใช้นี้มีอยู่ในระบบแล้ว',
        error: 'USERNAME_EXISTS'
      };
    }

    // Check if email already exists
    const existingEmail = users.find(user => user.email === email);
    if (existingEmail) {
      return {
        success: false,
        message: 'อีเมลนี้มีอยู่ในระบบแล้ว',
        error: 'EMAIL_EXISTS'
      };
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        success: false,
        message: 'รูปแบบอีเมลไม่ถูกต้อง',
        error: 'INVALID_EMAIL'
      };
    }

    // Phone number validation (optional)
    if (phoneNumber) {
      const phoneRegex = /^[0-9]{10}$/;
      if (!phoneRegex.test(phoneNumber.replace(/[-\s]/g, ''))) {
        return {
          success: false,
          message: 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง',
          error: 'INVALID_PHONE'
        };
      }
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user object
    const newUser = {
      id: users.length + 1,
      username,
      password: hashedPassword,
      name,
      surname,
      nickname: nickname || '',
      phoneNumber: phoneNumber || '',
      email,
      type,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    };

    // Save user (in production, save to database)
    users.push(newUser);

    // Generate JWT token
    const token = jwt.sign(
      {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        type: newUser.type
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Return success response (don't include password)
    const { password: _, ...userWithoutPassword } = newUser;

    return {
      success: true,
      message: 'ลงทะเบียนสำเร็จ',
      data: {
        user: userWithoutPassword,
        token,
        expiresIn: JWT_EXPIRES_IN
      }
    };

  } catch (error) {
    console.error('Registration error:', error);
    return {
      success: false,
      message: 'เกิดข้อผิดพลาดในการลงทะเบียน',
      error: 'INTERNAL_SERVER_ERROR'
    };
  }
};

/**
 * Get all users (for testing purposes)
 * @returns {Array} Array of users without passwords
 */
const getAllUsers = () => {
  return users.map(user => {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  });
};

/**
 * Find user by username
 * @param {string} username 
 * @returns {Object|null} User object or null
 */
const findUserByUsername = (username) => {
  return users.find(user => user.username === username) || null;
};

/**
 * Verify JWT token
 * @param {string} token 
 * @returns {Object} Decoded token or error
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

module.exports = {
  registerAdmin,
  getAllUsers,
  findUserByUsername,
  verifyToken,
  JWT_SECRET
};
