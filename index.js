const express = require('express');
const userRoutes = require('./user/userRoutes');
const productRoutes = require('./product/productRoutes');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic route
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to Magnet Admin API',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/admin', (req, res) => {
  res.json({
    status: 'admin',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// User routes
app.use('/api/user', userRoutes);

// Product routes
app.use('/api/product', productRoutes);

// API routes placeholder
app.get('/api', (req, res) => {
  res.json({
    message: 'API endpoints will be available here',
    version: '1.0.0',
    endpoints: {
      user: {
        'POST /api/user/register-admin': 'Register new admin user',
        'GET /api/user/users': 'Get all users (requires auth)',
        'GET /api/user/profile': 'Get current user profile (requires auth)',
        'POST /api/user/verify-token': 'Verify JWT token'
      },
      product: {
        'POST /api/product': 'Create new product (requires auth)',
        'GET /api/product': 'Get all products (requires auth)',
        'GET /api/product/:id': 'Get product by ID (requires auth)',
        'PUT /api/product/:id': 'Update product (requires auth)',
        'DELETE /api/product/:id': 'Delete product (requires auth)',
        'GET /api/product?search=query': 'Search products (requires auth)'
      }
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Magnet Admin API is running on port ${PORT}`);
  console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🕐 Started at: ${new Date().toISOString()}`);
});

module.exports = app;
