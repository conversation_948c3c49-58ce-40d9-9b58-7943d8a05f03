// In-memory storage for demo (in production, use database)
const products = [];

/**
 * Create Product Function
 * @param {Object} productData - Product data
 * @param {string} productData.productName - Product Name
 * @param {string} productData.companyName - Company Name
 * @param {string} productData.https - HTTPS URL
 * @param {string} productData.type - Product Type
 * @param {string} productData.description - Product Description
 * @returns {Object} Creation result
 */
const createProduct = async (productData) => {
  try {
    const {
      productName,
      companyName,
      https,
      type,
      description
    } = productData;

    // Validation
    if (!productName || !companyName || !https || !type || !description) {
      return {
        success: false,
        message: 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน',
        error: 'MISSING_REQUIRED_FIELDS'
      };
    }

    // Validate HTTPS URL
    const httpsRegex = /^https:\/\/.+/;
    if (!httpsRegex.test(https)) {
      return {
        success: false,
        message: 'URL ต้องเป็น HTTPS เท่านั้น',
        error: 'INVALID_HTTPS_URL'
      };
    }

    // Check if product name already exists
    const existingProduct = products.find(product => 
      product.productName.toLowerCase() === productName.toLowerCase()
    );
    if (existingProduct) {
      return {
        success: false,
        message: 'ชื่อสินค้านี้มีอยู่ในระบบแล้ว',
        error: 'PRODUCT_NAME_EXISTS'
      };
    }

    // Create product object
    const newProduct = {
      id: products.length + 1,
      productName: productName.trim(),
      companyName: companyName.trim(),
      https: https.trim(),
      type: type.trim(),
      description: description.trim(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    };

    // Save product (in production, save to database)
    products.push(newProduct);

    return {
      success: true,
      message: 'สร้างสินค้าสำเร็จ',
      data: newProduct
    };

  } catch (error) {
    console.error('Create product error:', error);
    return {
      success: false,
      message: 'เกิดข้อผิดพลาดในการสร้างสินค้า',
      error: 'INTERNAL_SERVER_ERROR'
    };
  }
};

/**
 * Get all products
 * @returns {Array} Array of products
 */
const getAllProducts = () => {
  return products.filter(product => product.isActive);
};

/**
 * Get product by ID
 * @param {number} id - Product ID
 * @returns {Object|null} Product object or null
 */
const getProductById = (id) => {
  return products.find(product => product.id === parseInt(id) && product.isActive) || null;
};

/**
 * Update product
 * @param {number} id - Product ID
 * @param {Object} updateData - Data to update
 * @returns {Object} Update result
 */
const updateProduct = async (id, updateData) => {
  try {
    const productIndex = products.findIndex(product => product.id === parseInt(id));
    
    if (productIndex === -1) {
      return {
        success: false,
        message: 'ไม่พบสินค้าที่ต้องการแก้ไข',
        error: 'PRODUCT_NOT_FOUND'
      };
    }

    const {
      productName,
      companyName,
      https,
      type,
      description
    } = updateData;

    // Validate HTTPS URL if provided
    if (https && !/^https:\/\/.+/.test(https)) {
      return {
        success: false,
        message: 'URL ต้องเป็น HTTPS เท่านั้น',
        error: 'INVALID_HTTPS_URL'
      };
    }

    // Check if new product name already exists (excluding current product)
    if (productName) {
      const existingProduct = products.find(product => 
        product.productName.toLowerCase() === productName.toLowerCase() && 
        product.id !== parseInt(id)
      );
      if (existingProduct) {
        return {
          success: false,
          message: 'ชื่อสินค้านี้มีอยู่ในระบบแล้ว',
          error: 'PRODUCT_NAME_EXISTS'
        };
      }
    }

    // Update product
    const updatedProduct = {
      ...products[productIndex],
      ...(productName && { productName: productName.trim() }),
      ...(companyName && { companyName: companyName.trim() }),
      ...(https && { https: https.trim() }),
      ...(type && { type: type.trim() }),
      ...(description && { description: description.trim() }),
      updatedAt: new Date().toISOString()
    };

    products[productIndex] = updatedProduct;

    return {
      success: true,
      message: 'แก้ไขสินค้าสำเร็จ',
      data: updatedProduct
    };

  } catch (error) {
    console.error('Update product error:', error);
    return {
      success: false,
      message: 'เกิดข้อผิดพลาดในการแก้ไขสินค้า',
      error: 'INTERNAL_SERVER_ERROR'
    };
  }
};

/**
 * Delete product (soft delete)
 * @param {number} id - Product ID
 * @returns {Object} Delete result
 */
const deleteProduct = async (id) => {
  try {
    const productIndex = products.findIndex(product => product.id === parseInt(id));
    
    if (productIndex === -1) {
      return {
        success: false,
        message: 'ไม่พบสินค้าที่ต้องการลบ',
        error: 'PRODUCT_NOT_FOUND'
      };
    }

    // Soft delete
    products[productIndex].isActive = false;
    products[productIndex].updatedAt = new Date().toISOString();

    return {
      success: true,
      message: 'ลบสินค้าสำเร็จ',
      data: { id: parseInt(id) }
    };

  } catch (error) {
    console.error('Delete product error:', error);
    return {
      success: false,
      message: 'เกิดข้อผิดพลาดในการลบสินค้า',
      error: 'INTERNAL_SERVER_ERROR'
    };
  }
};

/**
 * Search products by name or company
 * @param {string} query - Search query
 * @returns {Array} Array of matching products
 */
const searchProducts = (query) => {
  if (!query) return getAllProducts();
  
  const searchTerm = query.toLowerCase();
  return products.filter(product => 
    product.isActive && (
      product.productName.toLowerCase().includes(searchTerm) ||
      product.companyName.toLowerCase().includes(searchTerm) ||
      product.type.toLowerCase().includes(searchTerm)
    )
  );
};

module.exports = {
  createProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  searchProducts
};
