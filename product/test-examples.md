# Product API Test Examples

## Authentication Required
All product endpoints require JW<PERSON> token in Authorization header:
```
Authorization: Bearer YOUR_JWT_TOKEN
```

## 1. Create Product

```bash
curl -X POST http://localhost:3000/api/product \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "productName": "iPhone 15 Pro",
    "companyName": "Apple Inc.",
    "https": "https://www.apple.com/iphone-15-pro/",
    "type": "Smartphone",
    "description": "The latest iPhone with titanium design and A17 Pro chip"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "สร้างสินค้าสำเร็จ",
  "data": {
    "id": 1,
    "productName": "iPhone 15 Pro",
    "companyName": "Apple Inc.",
    "https": "https://www.apple.com/iphone-15-pro/",
    "type": "Smartphone",
    "description": "The latest iPhone with titanium design and A17 Pro chip",
    "createdAt": "2025-07-26T03:15:30.123Z",
    "updatedAt": "2025-07-26T03:15:30.123Z",
    "isActive": true
  }
}
```

## 2. Get All Products

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:3000/api/product
```

## 3. Get Product by ID

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:3000/api/product/1
```

## 4. Update Product

```bash
curl -X PUT http://localhost:3000/api/product/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "productName": "iPhone 15 Pro Max",
    "description": "Updated description for iPhone 15 Pro Max"
  }'
```

## 5. Delete Product

```bash
curl -X DELETE http://localhost:3000/api/product/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 6. Search Products

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "http://localhost:3000/api/product?search=iPhone"
```

## Required Fields for Product Creation

- **productName**: ชื่อสินค้า (required)
- **companyName**: ชื่อบริษัท (required)
- **https**: URL ของสินค้า (required, must start with https://)
- **type**: ประเภทสินค้า (required)
- **description**: รายละเอียดสินค้า (required)

## Error Examples

### Missing Required Fields
```json
{
  "success": false,
  "message": "กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน",
  "error": "MISSING_REQUIRED_FIELDS"
}
```

### Invalid HTTPS URL
```json
{
  "success": false,
  "message": "URL ต้องเป็น HTTPS เท่านั้น",
  "error": "INVALID_HTTPS_URL"
}
```

### Product Name Already Exists
```json
{
  "success": false,
  "message": "ชื่อสินค้านี้มีอยู่ในระบบแล้ว",
  "error": "PRODUCT_NAME_EXISTS"
}
```

### Product Not Found
```json
{
  "success": false,
  "message": "ไม่พบสินค้าที่ต้องการ",
  "error": "PRODUCT_NOT_FOUND"
}
```

### No Authorization Token
```json
{
  "success": false,
  "message": "ไม่พบ Access Token",
  "error": "NO_TOKEN"
}
```

## Complete Test Flow

1. **Register/Login to get token:**
```bash
curl -X POST http://localhost:3000/api/user/register-admin \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123",
    "confirmPassword": "password123",
    "name": "Admin",
    "surname": "User",
    "email": "<EMAIL>",
    "type": "admin"
  }'
```

2. **Use the token from response to create products:**
```bash
export TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

curl -X POST http://localhost:3000/api/product \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "productName": "MacBook Pro M3",
    "companyName": "Apple Inc.",
    "https": "https://www.apple.com/macbook-pro/",
    "type": "Laptop",
    "description": "Professional laptop with M3 chip"
  }'
```
