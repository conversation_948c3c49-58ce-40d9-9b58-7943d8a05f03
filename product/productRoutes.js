const express = require('express');
const { verifyToken } = require('../user/user');
const {
  createProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  searchProducts
} = require('./product');

const router = express.Router();

/**
 * Middleware to verify JWT token
 */
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'ไม่พบ Access Token',
      error: 'NO_TOKEN'
    });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(403).json({
      success: false,
      message: 'Access Token ไม่ถูกต้องหรือหมดอายุ',
      error: 'INVALID_TOKEN'
    });
  }

  req.user = decoded;
  next();
};

/**
 * POST /api/product
 * Create new product (protected route)
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const result = await createProduct(req.body);
    
    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * GET /api/product
 * Get all products (protected route)
 */
router.get('/', authenticateToken, (req, res) => {
  try {
    const { search } = req.query;
    let products;
    
    if (search) {
      products = searchProducts(search);
    } else {
      products = getAllProducts();
    }
    
    res.json({
      success: true,
      message: 'ดึงข้อมูลสินค้าสำเร็จ',
      data: products,
      total: products.length
    });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสินค้า',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * GET /api/product/:id
 * Get product by ID (protected route)
 */
router.get('/:id', authenticateToken, (req, res) => {
  try {
    const product = getProductById(req.params.id);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'ไม่พบสินค้าที่ต้องการ',
        error: 'PRODUCT_NOT_FOUND'
      });
    }
    
    res.json({
      success: true,
      message: 'ดึงข้อมูลสินค้าสำเร็จ',
      data: product
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลสินค้า',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * PUT /api/product/:id
 * Update product (protected route)
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const result = await updateProduct(req.params.id, req.body);
    
    if (result.success) {
      res.json(result);
    } else {
      const statusCode = result.error === 'PRODUCT_NOT_FOUND' ? 404 : 400;
      res.status(statusCode).json(result);
    }
  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการแก้ไขสินค้า',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * DELETE /api/product/:id
 * Delete product (protected route)
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const result = await deleteProduct(req.params.id);
    
    if (result.success) {
      res.json(result);
    } else {
      const statusCode = result.error === 'PRODUCT_NOT_FOUND' ? 404 : 400;
      res.status(statusCode).json(result);
    }
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'เกิดข้อผิดพลาดในการลบสินค้า',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

module.exports = router;
