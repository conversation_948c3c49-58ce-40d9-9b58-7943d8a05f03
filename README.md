# Magnet Admin API

Admin API for Magnet application built with Express.js and PM2.

## Features

- Express.js web framework
- PM2 process management
- Environment-based configuration
- Logging system
- Health check endpoints
- Error handling middleware

## Installation

```bash
npm install
```

## Development

### Start in development mode with auto-reload:
```bash
npm run dev
```

### Start normally:
```bash
npm start
```

## Production with PM2

### Start with PM2:
```bash
npm run pm2:start
```

### Other PM2 commands:
```bash
# Stop the application
npm run pm2:stop

# Restart the application
npm run pm2:restart

# Reload the application (zero-downtime)
npm run pm2:reload

# Delete the application from PM2
npm run pm2:delete

# View logs
npm run pm2:logs

# Monitor processes
npm run pm2:monit

# Check status
npm run pm2:status

# View combined logs
npm run logs
```

## API Endpoints

- `GET /` - Welcome message
- `GET /health` - Health check
- `GET /api` - API information

## Environment Variables

- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment (development/staging/production)

## Project Structure

```
├── index.js              # Main application file
├── ecosystem.config.js   # PM2 configuration
├── package.json          # Dependencies and scripts
├── logs/                 # Log files directory
├── .gitignore           # Git ignore rules
└── README.md            # This file
```

## Logging

Logs are stored in the `logs/` directory:
- `combined.log` - All logs
- `out.log` - Standard output
- `error.log` - Error logs

## PM2 Configuration

The PM2 configuration is in `ecosystem.config.js` and includes:
- Auto-restart on crashes
- Memory limit monitoring
- Environment-specific settings
- Log rotation
- Deployment configuration

## Development Tips

1. Use `npm run dev` for development with auto-reload
2. Use `npm run pm2:start` for production-like testing
3. Monitor logs with `npm run pm2:logs` or `npm run logs`
4. Check application status with `npm run pm2:status`

## License

ISC
